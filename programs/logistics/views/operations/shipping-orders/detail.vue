<template>
    <ui-view
        ref="view"
        type="form"
        collection="logistics.shipping-orders"
        method="logistics.shipping-orders-save"
        :model="model"
        :title="title"
        :extra-fields="extraFields"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        :actions="actions"
        :extra-actions="extraActions"
        @changed="handleChange"
        v-if="initialized"
    >
        <template slot="form-top">
            <ui-status :statuses="statuses" :value="status" />

            <ui-related-documents :documents="model.relatedDocuments" />

            <el-button
                :loading="$params('loading')"
                type="primary"
                plain
                icon="far fa-print"
                :disabled="!$params('id') || !$params('isPreview')"
                @click="handlePrint"
            >
                {{ $t('Print Label') }}
            </el-button>

            <el-button
                :loading="$params('loading')"
                type="primary"
                icon="far fa-thumbs-up"
                :disabled="!$params('isPreview') || !canApproveOrder"
                @click="handleApprove"
            >
                {{ $t('Approve Order') }}
            </el-button>
        </template>

        <div class="columns">
            <div class="column is-half">
                <ui-field name="code" disabled />
                <ui-field name="type" :options="typeOptions" translate-labels />
                <ui-field name="status" :options="statusOptions" translate-labels v-show="false" />
                <ui-field
                    name="carrierId"
                    collection="logistics.carriers"
                    view="logistics.configuration.carriers"
                    :filters="{
                        $and: [
                            {integrationType: {$exists: true}},
                            {integrationType: {$ne: null}},
                            {integrationType: {$ne: ''}}
                        ]
                    }"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                />
                <kernel-common-partner-select name="partnerId" />
                <ui-field
                    name="transferId"
                    collection="inventory.transfers"
                    view="inventory.operations.transfers"
                    :filters="{type: 'outgoing', $sort: {recordDate: -1}}"
                    label-from="code"
                />
                <ui-field
                    name="orderId"
                    collection="sale.orders"
                    view="sale.sales.orders"
                    :filters="{$sort: {recordDate: -1}}"
                    label-from="code"
                />
                <ui-field
                    name="contactPersonId"
                    collection="kernel.contacts"
                    view="partners.contacts"
                    :filters="contactPersonIdFilters"
                    :update-params="updateContactIdParams"
                />
            </div>

            <div class="column is-half">
                <kernel-common-branch-select />
                <ui-field name="currencyId" collection="kernel.currencies" v-show="$setting('system.multiCurrency')" />
                <ui-field name="cashOnDeliveryAmount" :precision="$setting('system.currencyPrecision')">
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field name="recordDate" />
                <ui-field name="issueDate" />
                <ui-field name="deliveryDate" />
                <ui-field name="deliveredAt" disabled />
                <div style="display: flex; max-width: 450px">
                    <ui-field
                        name="estimatedCost"
                        :precision="$setting('system.currencyPrecision')"
                        style="flex: 1 1 0"
                        disabled
                    >
                        <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                            {{ currencyFormat.currency.symbol }}
                        </div>
                    </ui-field>
                    <el-button
                        class="mb5 ml5"
                        style="flex: 0 0 25px"
                        icon="far fa-calculator"
                        :title="'Calculate' | t"
                        :disabled="!$params('isPreview')"
                        @click="handleCalculate"
                    />
                </div>
            </div>
        </div>

        <el-tabs v-model="activeTab" class="mt10">
            <el-tab-pane name="items" :label="'Items' | t">
                <ui-field
                    name="items"
                    class="mb0"
                    :schema="itemsSchema"
                    :min-empty-rows="5"
                    :before-init="beforeItemsInit"
                    :before-create="beforeSaveItem"
                    :before-update="beforeSaveItem"
                    :after-create="afterSaveItem"
                    :after-update="afterSaveItem"
                    :enable-enlarge="true"
                    :disabled="!model.carrierId || !model.partnerId"
                    resizable
                >
                    <template slot="actions">
                        <el-button
                            :loading="$params('loading')"
                            plain
                            icon="far fa-broom"
                            :disabled="$params('isPreview') || !model.carrierId || (model.items || []).length < 1"
                            @click="handleRemoveAllItems"
                        >
                            {{ 'Remove All Items' | t }}
                        </el-button>
                    </template>
                </ui-field>
            </el-tab-pane>

            <el-tab-pane name="products" :label="'Products' | t" v-if="!!model.products && model.products.length > 0">
                <ui-table
                    :items="model.products"
                    :columns="productsColumns"
                    :enable-auto-height="true"
                    :enable-selection="false"
                    disable-no-rows-overlay
                    v-if="!!model.products && model.products.length > 0"
                />
            </el-tab-pane>

            <el-tab-pane name="details" :label="'Details' | t">
                <div class="columns mt10">
                    <div class="column is-half">
                        <ui-legend title="General" />
                        <ui-field name="shippingPaymentType" :options="shippingPaymentTypeOptions" translate-labels />
                        <ui-field name="packagingType" :options="packagingTypeOptions" translate-labels />
                        <ui-field
                            name="warehouseId"
                            collection="inventory.warehouses"
                            :filters="{branchId: model.branchId}"
                            :extra-fields="['shortName']"
                            :template="'{{shortName}} - {{name}}'"
                        />

                        <ui-legend title="Note" class="mt30" />
                        <ui-field name="note" label="hide" :rows="2" />
                    </div>

                    <div class="column is-half">
                        <ui-legend title="Delivery Address" />
                        <ui-field
                            name="deliveryAddress"
                            field-type="compact-address"
                            label="hide"
                            :is-preview="$params('isPreview')"
                        />

                        <ui-legend title="Warehouse Address" class="mt30" />
                        <ui-field
                            name="warehouseAddress"
                            field-type="compact-address"
                            label="hide"
                            :is-preview="$params('isPreview')"
                        />
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane name="records" :label="'Records' | t">
                <ui-table
                    :items="model.records || []"
                    :columns="recordsColumns"
                    :enable-auto-height="true"
                    :enable-selection="false"
                />
            </el-tab-pane>

            <el-tab-pane name="attachments" :label="$t('Attachments')">
                <ui-field name="attachments" field-type="attachments" auto-height />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import PrintPopup from '@/kernel/frontend/components/ui/view/print-popup';
import Random from '../../../../../framework/random';

export default {
    data: () => ({
        model: {},
        extraFields: ['status', 'records', 'volumetricWeightFactor', 'relatedDocuments', 'products', 'integrationType'],
        typeOptions: [
            {value: 'normal', label: 'Normal order'},
            {value: 'return', label: 'Return order'}
        ],
        statusOptions: [
            {value: 'draft', label: 'Draft'},
            {value: 'order-created', label: 'Order created'},
            {value: 'shipment-prepared', label: 'Shipment prepared'},
            {value: 'in-transfer-phase', label: 'In transfer phase'},
            {value: 'arrived-at-the-delivery-unit', label: 'Arrived at the delivery unit'},
            {value: 'forwarded-to-recipient-address', label: 'Forwarded to recipient address'},
            {value: 'delivered', label: 'Delivered'},
            {value: 'failed-to-deliver', label: 'Failed to deliver'},
            {value: 'returning', label: 'Returning'},
            {value: 'returned', label: 'Returned'},
            {value: 'canceled', label: 'Canceled'}
        ],
        shippingPaymentTypeOptions: [
            {value: 'freight-prepaid', label: 'Freight prepaid'},
            {value: 'freight-collect', label: 'Freight collect'}
        ],
        packagingTypeOptions: [
            {value: 'package', label: 'Package'},
            {value: 'parcel', label: 'Parcel'},
            {value: 'file', label: 'File'},
            {value: 'mb', label: 'Manifest bag'}
        ],
        activeTab: 'items',
        currencyFormat: null,
        initialized: false
    }),

    computed: {
        title() {
            const model = this.model;

            if (this.$params('id')) {
                return model.code ? model.code : '';
            }

            return this.$t('New Shipping Order');
        },
        actions() {
            return this.$params('id') && this.status !== 'draft' ? 'edit:disabled,cancel' : 'edit,cancel';
        },
        extraActions() {
            return [
                {
                    name: 'cancel-order',
                    title: 'Cancel Order',
                    icon: 'fal fa-ban',
                    handler: this.handleCancel,
                    disabled: () => {
                        return !this.$params('id') || this.status === 'canceled';
                    }
                }
            ];
        },
        canApproveOrder() {
            const status = this.status;

            return (
                !!this.$params('id') &&
                !this.$params('loading') &&
                !!this.$params('isPreview') &&
                ['draft'].includes(status)
            );
        },
        statuses() {
            const status = this.model.status;
            const statuses = [
                {value: 'draft', label: 'Draft'},
                {value: 'order-created', label: 'Order created'}
            ];

            if (status === 'shipment-prepared') statuses.push({value: 'shipment-prepared', label: 'Shipment prepared'});
            if (status === 'in-transfer-phase') statuses.push({value: 'in-transfer-phase', label: 'In transfer phase'});
            if (status === 'arrived-at-the-delivery-unit')
                statuses.push({
                    value: 'arrived-at-the-delivery-unit',
                    label: 'Arrived at the delivery unit'
                });
            if (status === 'forwarded-to-recipient-address')
                statuses.push({
                    value: 'forwarded-to-recipient-address',
                    label: 'Forwarded to recipient address'
                });
            if (status === 'delivered') statuses.push({value: 'delivered', label: 'Delivered'});
            if (status === 'failed-to-deliver') statuses.push({value: 'failed-to-deliver', label: 'Failed to deliver'});
            if (status === 'returning') statuses.push({value: 'returning', label: 'Returning'});
            if (status === 'returned') statuses.push({value: 'returned', label: 'Returned'});
            if (status === 'canceled') statuses.push({value: 'canceled', label: 'Canceled'});

            return statuses;
        },
        status() {
            const model = this.model;

            return model.status;
        },
        contactPersonIdFilters() {
            if (!!this.model.partnerId) {
                return {partnerId: this.model.partnerId, type: 'contact'};
            }

            return {type: 'contact'};
        },
        itemsSchema() {
            const self = this;

            return {
                id: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                packageId: {
                    type: 'string',
                    label: 'Package',
                    required: false,
                    column: {
                        populate: 'package',
                        width: 180
                    },
                    editor: {
                        collection: 'logistics.packages',
                        view: 'logistics.operations.packages',
                        labelFrom: 'code',
                        filters: () => {
                            return {
                                carrierId: this.model.carrierId,
                                partnerId: this.model.partnerId,
                                isUsed: false
                            };
                        },
                        updateParams: (params, type) => {
                            if (type === 'create' || type === 'detail') {
                                params.model = {
                                    carrierId: this.model.carrierId,
                                    partnerId: this.model.partnerId
                                };
                            } else if (type === 'list') {
                                params.filters = {
                                    carrierId: this.model.carrierId,
                                    partnerId: this.model.partnerId,
                                    isUsed: false
                                };
                            }

                            return params;
                        }
                    }
                },
                barcode: {
                    type: 'string',
                    label: 'Barcode',
                    required: false,
                    column: {
                        width: 180,
                        visible: true
                    },
                    editor: {}
                },
                description: {
                    type: 'string',
                    label: 'Description',
                    required: false,
                    column: {
                        minWidth: 150,
                        visible: true
                    },
                    editor: {}
                },
                quantity: {
                    type: 'decimal',
                    label: 'Quantity',
                    default: 1,
                    column: {
                        width: 100,
                        suppressMenu: true,
                        format: 'unit',
                        formatOptions(row) {
                            return {number: {precision: self.$setting('system.unitPrecision')}};
                        }
                    },
                    editor: {}
                },
                width: {
                    type: 'decimal',
                    label: 'Width (cm)',
                    default: 0,
                    column: {
                        width: 120,
                        suppressMenu: true,
                        visible: false,
                        format: 'unit',
                        formatOptions(row) {
                            return {number: {precision: self.$setting('system.unitPrecision')}};
                        }
                    },
                    editor: {}
                },
                height: {
                    type: 'decimal',
                    label: 'Height (cm)',
                    default: 0,
                    column: {
                        width: 120,
                        suppressMenu: true,
                        visible: false,
                        format: 'unit',
                        formatOptions(row) {
                            return {number: {precision: self.$setting('system.unitPrecision')}};
                        }
                    },
                    editor: {}
                },
                depth: {
                    type: 'decimal',
                    label: 'Depth (cm)',
                    default: 0,
                    column: {
                        width: 120,
                        suppressMenu: true,
                        visible: false,
                        format: 'unit',
                        formatOptions(row) {
                            return {number: {precision: self.$setting('system.unitPrecision')}};
                        }
                    },
                    editor: {}
                },
                weight: {
                    type: 'decimal',
                    label: 'Weight (kg)',
                    default: 1,
                    column: {
                        width: 120,
                        suppressMenu: true,
                        format: 'unit',
                        formatOptions(row) {
                            return {number: {precision: self.$setting('system.unitPrecision')}};
                        }
                    },
                    editor: {}
                },
                volumetricWeight: {
                    type: 'decimal',
                    label: 'Volumetric weight',
                    default: 1,
                    column: {
                        width: 90,
                        suppressMenu: true,
                        format: 'unit',
                        formatOptions(row) {
                            return {number: {precision: self.$setting('system.unitPrecision')}};
                        }
                    }
                }
            };
        },
        recordsColumns() {
            return [
                {field: 'date', label: 'Date', format: 'datetime', width: 155},
                {
                    field: 'status',
                    label: 'Status',
                    valueLabels: [
                        {value: 'order-created', label: 'Order created'},
                        {value: 'shipment-prepared', label: 'Shipment prepared'},
                        {value: 'in-transfer-phase', label: 'In transfer phase'},
                        {value: 'arrived-at-the-delivery-unit', label: 'Arrived at the delivery unit'},
                        {value: 'forwarded-to-recipient-address', label: 'Forwarded to recipient address'},
                        {value: 'delivered', label: 'Delivered'},
                        {value: 'failed-to-deliver', label: 'Failed to deliver'}
                    ],
                    minWidth: 150
                },
                {field: 'location', label: 'Location', width: 180},
                {field: 'locationPhone', label: 'Location phone', width: 180},
                {field: 'locationAddress', label: 'Location address', minWidth: 150}
            ];
        },
        productsColumns() {
            const self = this;

            return [
                {
                    field: 'packageCode',
                    label: 'Package',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data && data.packageId) {
                            return {
                                id: data.packageId,
                                view: 'logistics.operations.packages'
                            };
                        }

                        return {};
                    },
                    width: 190
                },
                {
                    field: 'productCode',
                    label: 'Code',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data && data.productId) {
                            return {
                                id: data.productId,
                                view: 'inventory.catalog.products-detail'
                            };
                        }

                        return {};
                    },
                    width: 120
                },
                {
                    field: 'productDefinition',
                    label: 'Definition',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data && data.productId) {
                            return {
                                id: data.productId,
                                view: 'inventory.catalog.products-detail'
                            };
                        }

                        return {};
                    },
                    minWidth: 210
                },
                {
                    field: 'unitName',
                    label: 'Unit',
                    width: 75
                },
                {
                    field: 'quantity',
                    label: 'Quantity',
                    width: 90,
                    format: 'unit',
                    formatOptions() {
                        return {number: {precision: self.$setting('system.unitPrecision')}};
                    }
                }
            ];
        }
    },

    methods: {
        async handleApprove() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to approve the document. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);
            this.$params('isPreview', true);

            try {
                await this.$rpc('logistics.shipping-orders-approve', this.$params('id'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleCalculate() {
            this.$params('loading', true);
            this.$params('isPreview', true);

            try {
                await this.$rpc('logistics.shipping-orders-calculate', this.$params('id'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleCancel() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to cancel the document. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);
            this.$params('isPreview', true);

            try {
                await this.$rpc('logistics.shipping-orders-cancel', this.$params('id'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handlePrint() {

            const carrier = await this.$collection('logistics.carriers').findOne({
                _id: this.model.carrierId,
                $select: ['printingMode', 'integrationType']
            });

            const printingMode = carrier?.printingMode || 'system-template';
                    let resultUrl;

                    if (printingMode === 'hepsijet-system-template') {
                        resultUrl = await this.$rpc('logistics.shipping-orders-get-merged-label', {
                            id: this.$params('id')
                        });

                          this.$program.dialog({
                        component: PrintPopup,
                        params: {title: this.$t('Shipping Order'), url: resultUrl}
                    });
                    }
                    else if(printingMode === 'system-template'){
                        this.$refs.view.handlePrint({
                source: 'logistics.shipping-order-label',
                title: this.$t('Shipping Order')
            });
                    }
                    else {
                        resultUrl = await this.$rpc('logistics.shipping-orders-get-barcode', {
                            id: this.$params('id')
                        });
                        console.log('resultUrl', resultUrl)
                          this.$program.dialog({
                        component: PrintPopup,
                        params: {title: this.$t('Shipping Order'), url: resultUrl}
                    });
                    }


        },
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];

            // Default currency
            if (!model.currencyId) {
                model.currencyId = company.currencyId;
            }

            // Currency format.
            if (company.currencyId !== model.currencyId) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: model.currencyId,
                    $select: ['name', 'symbol', 'symbolPosition']
                });

                if (_.isObject(currency)) {
                    this.currencyFormat = {
                        currency: {
                            symbol: currency.symbol,
                            format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s',
                            symbolPosition: currency.symbolPosition
                        }
                    };
                }
            }

            return model;
        },
        async beforeValidate(model) {
            return model;
        },
        async beforeSubmit(model) {
            return model;
        },
        async handleChange(model, field) {
            if (field === 'currencyId' && !!model.currencyId) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: model.currencyId,
                    $select: ['name', 'symbol', 'symbolPosition']
                });

                if (_.isObject(currency)) {
                    this.currencyFormat = {
                        currency: {
                            symbol: currency.symbol,
                            format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s',
                            symbolPosition: currency.symbolPosition
                        }
                    };
                }
            }
            if (field === 'carrierId' && !!model.carrierId) {
                const carrier = await this.$collection('logistics.carriers').findOne({
                    _id: model.carrierId,
                    $select: ['volumetricWeightFactor', 'integrationType'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                this.model.volumetricWeightFactor = carrier.volumetricWeightFactor;

                try {
                    this.model.code = await this.$rpc('logistics.shipping-orders-get-code', {
                        carrierId: model.carrierId
                    });
                } catch (error) {
                    this.model.code = '';
                    this.$program.message('error', error.message);
                }
            }
            if (field === 'partnerId' && !!model.partnerId) {
                const partner = await this.$collection('kernel.partners').findOne({
                    _id: model.partnerId,
                    $select: ['address', 'defaultDeliveryOptionId'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                let deliveryAddress = partner.address;

                const shippingContact = await this.$collection('kernel.contacts').findOne({
                    partnerId: model.partnerId,
                    type: 'delivery-address',
                    $select: ['address']
                });
                if (!!shippingContact) {
                    deliveryAddress = shippingContact.address;
                }

                if (partner.defaultDeliveryOptionId) {
                    this.model.carrierId = partner.defaultDeliveryOptionId;
                    this.handleChange(this.model, 'carrierId');
                }

                this.model.deliveryAddress = deliveryAddress;
            }
            if (field === 'warehouseId' && !!model.warehouseId) {
                const warehouse = await this.$collection('inventory.warehouses').findOne({
                    _id: model.warehouseId,
                    $select: ['address'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                this.model.warehouseAddress = warehouse.address;
            }
        },
        async beforeItemsInit(items) {
            let packageIds = [];
            let packages = [];
            items.forEach(item => {
                if (item.packageId) packageIds.push(item.packageId);
            });
            packageIds = _.uniq(packageIds);
            if (packageIds.length > 0) {
                packages = await this.$collection('logistics.packages').find({
                    _id: {$in: packageIds},
                    $select: ['code', 'barcode', 'width', 'height', 'depth', 'grossWeight'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }

            return items.map(item => {
                if (item.packageId && packages.length > 0) {
                    item.package = packages.find(pkg => pkg._id === item.packageId);
                }

                return item;
            });
        },
        async beforeSaveItem({row, params}) {
            const round = n => this.$app.roundNumber(n, 4);
            let field = params.colDef.field;

            // Row id.
            if (!row.id) {
                row.id = Random.id(8);
            }

            if (_.isString(row.packageId)) {
                const pkg = await this.$collection('logistics.packages').findOne({
                    _id: row.packageId,
                    $select: [
                        'code',
                        'barcode',
                        'width',
                        'widthUnitId',
                        'height',
                        'heightUnitId',
                        'depth',
                        'depthUnitId',
                        'grossWeight',
                        'grossWeightUnitId'
                    ]
                });
                row.package = pkg;

                if (field === 'packageId' && !!pkg) {
                    row.barcode = pkg.barcode;

                    const units = await this.$collection('kernel.units').find({
                        _id: {$in: [pkg.grossWeightUnitId, pkg.widthUnitId, pkg.heightUnitId, pkg.depthUnitId]},
                        $disableSoftDelete: true,
                        $disableActiveCheck: true
                    });
                    const unitsMap = {};
                    for (const unit of units) {
                        unitsMap[unit._id] = unit;
                    }

                    const widthUnit = unitsMap[pkg.widthUnitId];
                    const heightUnit = unitsMap[pkg.heightUnitId];
                    const depthUnit = unitsMap[pkg.depthUnitId];
                    const grossWeightUnit = unitsMap[pkg.grossWeightUnitId];
                    let width = 0;
                    let height = 0;
                    let depth = 0;
                    let weight = 0;

                    if (widthUnit) {
                        if (widthUnit.type === 'smaller') width = round(round(pkg.width) / round(widthUnit.ratio));
                        else if (widthUnit.type === 'reference') width = round(pkg.width);
                        else if (widthUnit.type === 'bigger') width = round(pkg.width) * round(widthUnit.ratio);
                    }
                    if (heightUnit) {
                        if (heightUnit.type === 'smaller') height = round(round(pkg.height) / round(heightUnit.ratio));
                        else if (heightUnit.type === 'reference') height = round(pkg.height);
                        else if (heightUnit.type === 'bigger') height = round(pkg.height) * round(heightUnit.ratio);
                    }
                    if (depthUnit) {
                        if (depthUnit.type === 'smaller') depth = round(round(pkg.depth) / round(depthUnit.ratio));
                        else if (depthUnit.type === 'reference') depth = round(pkg.depth);
                        else if (depthUnit.type === 'bigger') depth = round(pkg.depth) * round(depthUnit.ratio);
                    }
                    if (grossWeightUnit) {
                        if (grossWeightUnit.type === 'smaller')
                            weight = round(round(pkg.grossWeight) / round(grossWeightUnit.ratio));
                        else if (grossWeightUnit.type === 'reference') weight = round(pkg.grossWeight);
                        else if (grossWeightUnit.type === 'bigger')
                            weight = round(pkg.grossWeight) * round(grossWeightUnit.ratio);
                    }

                    width = round(width * 100);
                    height = round(height * 100);
                    depth = round(depth * 100);

                    row.width = width;
                    row.height = height;
                    row.depth = depth;
                    row.weight = weight;
                    row.volumetricWeight = round(
                        (row.width * row.height * row.depth) / (this.model.volumetricWeightFactor || 5000)
                    );
                }
            }

            if (field !== 'volumetricWeight') {
                row.volumetricWeight = round(
                    (row.width * row.height * row.depth) / (this.model.volumetricWeightFactor || 5000)
                );
            }

            return row;
        },
        async afterSaveItem() {},
        handleRemoveAllItems() {
            this.$set(this.model, 'items', []);
            this.afterSaveItem();
        },
        updateContactIdParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'contact'};

                if (!!this.model.partnerId) {
                    params.partnerId = this.model.partnerId;
                }
            } else if (type === 'list') {
                params.filters = {type: 'contact'};

                if (!!this.model.partnerId) {
                    params.filters.partnerId = this.model.partnerId;
                }
            }

            return params;
        }
    },

    async created() {
        this.$params('loading', true);

        const company = this.$store.getters['session/company'];
        this.currencyFormat = {
            currency: {
                symbol: company.currency.symbol,
                format: company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s',
                symbolPosition: company.currency.symbolPosition
            }
        };

        this.initialized = true;

        this.$params('loading', false);
    }
};
</script>
