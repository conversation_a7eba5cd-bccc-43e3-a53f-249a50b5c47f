export default {
    name: 'geocoded',
    uid: false,
    softDelete: false,
    sync: false,
    noCache: true,
    schema: {
        address: {
            type: 'string',
            index: true
        },
        lat: {
            type: 'number',
            index: true
        },
        lng: {
            type: 'number',
            index: true
        }
    },
    publish(app, data, context) {
        return false;
    }
};
