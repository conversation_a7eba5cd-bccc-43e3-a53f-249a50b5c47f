export default {
    name: 'document-locations',
    softDelete: false,
    sync: false,
    noCache: true,
    extraIndexes: [
        {type: '2dsphere', key: 'location'},
        {type: '2dsphere', key: 'createLocation'},
        {type: '2dsphere', key: 'updateLocation'},
        {type: 'normal', key: 'document.*', value: 1}
        // {type: 'normal', key: 'document.status', value: 1},
        // {type: 'normal', key: 'document.code', value: 1},
        // {type: 'normal', key: 'document.type', value: 1},
        // {type: 'normal', key: 'document.documentTypeId', value: 1},
        // {type: 'normal', key: 'document.date', value: 1},
        // {type: 'normal', key: 'document.sourceId', value: 1},
        // {type: 'normal', key: 'document.communicationChannelId', value: 1},
        // {type: 'normal', key: 'document.branchId', value: 1},
        // {type: 'normal', key: 'document.organizationId', value: 1},
        // {type: 'normal', key: 'document.salesManagerId', value: 1},
        // {type: 'normal', key: 'document.salespersonId', value: 1},
        // {type: 'normal', key: 'document.total', value: 1}
    ],
    schema: {
        location: {
            type: 'object',
            blackbox: true
        },
        locationLat: {
            type: 'number',
            index: true
        },
        locationLng: {
            type: 'number',
            index: true
        },
        locationHash: {
            type: 'string',
            index: true
        },
        createLocation: {
            type: 'object',
            blackbox: true,
            required: false
        },
        createLocationLat: {
            type: 'number',
            required: false,
            index: true
        },
        createLocationLng: {
            type: 'number',
            required: false,
            index: true
        },
        updateLocation: {
            type: 'object',
            blackbox: true,
            required: false
        },
        updateLocationLat: {
            type: 'number',
            required: false,
            index: true
        },
        updateLocationLng: {
            type: 'number',
            required: false,
            index: true
        },
        locationToCreateDistance: {
            // Km
            type: 'number',
            required: false,
            index: true
        },
        locationToUpdateDistance: {
            // Km
            type: 'number',
            required: false,
            index: true
        },
        createToUpdateDistance: {
            // Km
            type: 'number',
            required: false,
            index: true
        },
        documentCollection: {
            type: 'string',
            index: true
        },
        documentId: {
            type: 'string',
            index: true
        },
        document: {
            type: 'object',
            blackbox: true,
            required: false
        },
        userId: {
            type: 'string',
            required: false,
            index: true
        }
    },
    publish(app, data, context) {
        return false;
    }
};
