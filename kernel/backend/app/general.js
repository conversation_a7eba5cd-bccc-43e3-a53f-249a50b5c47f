import cluster from 'cluster';
import _ from 'lodash';
import request from 'request';
import fs from 'fs-extra';
import shell from 'shelljs';
import archiver from 'archiver';
import decompress from 'decompress';
import Cache from 'framework/cache';
import MemoryCache from 'framework/memory-cache';
import process from 'node:process';
import Big from 'big.js';
import GoogleMaps from '@google/maps';
import {trim} from 'framework/helpers';
import notepack from 'notepack.io';

export default async function (app) {
    app.isMaster = (() => {
        // Is run with PM2.
        if (!!process.env && process.env.NODE_APP_INSTANCE === '0') {
            return true;
        }

        return cluster.isMaster;
    })();

    app.hasModule = module => {
        const license = app.get('license');

        if (!!license) {
            const licenseModule = (license.modules || []).find(m => m.name === module);

            if (!!licenseModule) {
                return !!licenseModule.isActive;
            }

            return true;
        }

        return true;
    };
    app.hasFeature = feature => {
        const license = app.get('license');

        if (!!license) {
            const licenseFeature = (license.features || []).find(f => f.name === feature);

            if (!!licenseFeature) {
                return !!licenseFeature.isActive;
            }

            return true;
        }

        return true;
    };

    app.exec = (command, options = {}) => {
        return new Promise((resolve, reject) => {
            options = {
                ...(options || {}),
                async: true
            };

            shell.exec(command, options, (code, stdout, stderr) => {
                if (code === 0) {
                    resolve(stdout);
                } else {
                    reject(stderr);
                }
            });
        });
    };

    app.archive = (src, dest, options = {}) => {
        return new Promise(async (resolve, reject) => {
            const output = fs.createWriteStream(dest);
            const archive = archiver(
                'zip',
                _.defaults(options, {
                    zlib: {level: 9} // Sets the compression level.
                })
            );
            output.on('close', () => {
                resolve();
            });
            output.on('error', error => {
                reject(error);
            });
            archive.pipe(output);
            archive.directory(src, false);
            archive.finalize();
        });
    };

    app.extract = (src, dest) => {
        return decompress(src, dest);
    };

    app.absoluteUrl = (url = null) => {
        let str = `${app.config('server.protocol')}://${app.config('server.host')}`;

        if (app.config('server.port') !== 80) {
            str += `:${app.config('server.port')}`;
        }

        if (!!process.env.DEV_SERVER) {
            str = 'http://**************:3000';
        }

        if (url) {
            str += `/${trim(url, '/')}`;
        }

        return str;
    };

    app.cache = new Cache();
    app.realTimeEventSubscription = app.cache.client.duplicate();
    app.realTimeEventSubscription.subscribe('real-time-events');

    app.memoryCache = new MemoryCache();
    const originalCacheClearMethod = app.memoryCache.clear.bind(app.memoryCache);
    app.memoryCache.clear = (pattern = '', syncCache = true) => {
        originalCacheClearMethod(pattern);

        if (syncCache) {
            app.cache.client.publish(
                'real-time-events',
                notepack.encode({
                    event: 'kernel:clear-memory-cache',
                    data: {
                        pid: process.pid,
                        cacheKey: pattern
                    }
                })
            );
        }
    };

    app.roundNumber = (number, precision = 0) => {
        precision = _.isNumber(precision) ? precision : app.setting('system.amountPrecision');

        return Number(Big(number).round(precision));
    };
    app.roundNumberComplex = (number, precision = 0, rounding = null) => {
        precision = _.isNumber(precision) ? precision : app.setting('system.amountPrecision');

        if (rounding === 'up') {
            return Number(Big(number).round(precision, Big.roundUp));
        } else if (rounding === 'down') {
            return Number(Big(number).round(precision, Big.roundDown));
        }

        return Number(Big(number).round(precision));
    };

    app.defaultAccountingAccount = (key, module = null) => {
        const defaultAccounts = app.setting('system.defaultAccounts');
        const entry =
            module !== null
                ? defaultAccounts.find(account => account.key === key && account.module === module)
                : defaultAccounts.find(account => account.key === key);

        if (_.isObject(entry)) {
            return entry.accountId;
        }
    };

    const progressFn = payload => {
        app.io.emit('kernel:progress-status', payload);
    };
    const progressFnDebounced = _.debounce(progressFn, 25, {
        leading: false,
        trailing: true
    });
    app.progress = payload => {
        if (payload.status === 'success' || payload.status === 'error' || payload.status === 'started') {
            progressFn(payload);
        } else {
            progressFnDebounced(payload);
        }
    };

    app.sendPushNotification = (userId, title, message, data = {}) => {
        return new Promise(async (resolve, reject) => {
            const {mobileNotificationsToken} = await app.collection('kernel.users').get(userId);

            if (!mobileNotificationsToken) {
                return resolve();
            }

            request(
                {
                    json: true,
                    method: 'POST',
                    url: 'https://onesignal.com/api/v1/notifications',
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: 'Basic NGVmNjRkNDgtMWYwNC00OWVjLWFiZDItYzBjN2E4MjMzZDBj'
                    },
                    body: {
                        app_id: '366f6aaf-acb8-4637-b8ee-6d3cc3af8f67',
                        headings: {en: title},
                        contents: {en: message},
                        data,
                        include_player_ids: [mobileNotificationsToken]
                    }
                },
                (error, response, body) => {
                    if (!!error) return reject(error);

                    if (response.statusCode === 200) {
                        if (body.recipients > 0) {
                            resolve();
                        } else {
                            if (!!body.errors && body.errors.length > 0) {
                                reject({
                                    code: response.statusCode,
                                    message: body.errors[0]
                                });
                            } else {
                                reject({
                                    code: response.statusCode,
                                    message: 'Notification could not sent!'
                                });
                            }
                        }
                    } else {
                        if (!!body.errors && body.errors.length > 0) {
                            reject({
                                code: response.statusCode,
                                message: body.errors[0]
                            });
                        } else {
                            reject({
                                code: response.statusCode,
                                message: response.statusMessage
                            });
                        }
                    }
                }
            );
        });
    };

    app.geocode = address => {
        const googleMapsApiKey = app.setting('system.googleMapsApiKey');

        if (_.isString(googleMapsApiKey) && !!googleMapsApiKey.trim()) {
            return new Promise(async (resolve, reject) => {
                const geocoded = await app.collection('kernel.geocoded').findOne({address});

                if (_.isPlainObject(geocoded)) {
                    resolve(_.omit(geocoded, 'address'));
                } else {
                    try {
                        const googleMapsClient = GoogleMaps.createClient({
                            key: googleMapsApiKey.trim(),
                            Promise
                        });
                        const response = (await googleMapsClient.geocode({address}).asPromise()).json;

                        if (response.status === 'OK' && response.results.length > 0) {
                            const result = response.results[0];
                            const location = {
                                lat: result.geometry.location.lat,
                                lng: result.geometry.location.lng
                            };

                            await app.collection('kernel.geocoded').create(
                                {
                                    address,
                                    ...location
                                },
                                {disableValidation: true}
                            );

                            resolve(location);
                        } else {
                            reject(new app.errors.Unprocessable('Can not geocode the address!'));
                        }
                    } catch (error) {
                        reject(error);
                    }
                }
            });
        }

        return Promise.reject(new app.errors.Unprocessable('No google api key!'));
    };

    app.set('clientVersion', global.entererpClientVersion);
}
