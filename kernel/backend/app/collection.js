// @TODO -> Searches on large collection is very slow. Current search implementation ($regex) is wrong for such collections. Implement $text search.

import path from 'path';
import _ from 'lodash';
import fastCopy from 'fast-copy';
import {ObjectId} from 'mongodb';
import sift from 'sift';
import BatchLoader from '@feathers-plus/batch-loader';
import * as cheerio from 'cheerio';
import memoryService from 'framework/services/memory';
import {when, fastJoin, getItems, makeCallingParams, replaceItems} from 'feathers-hooks-common';
import dbService from 'framework/services/database';
import ngrams from 'framework/services/ngrams';
import Collection from 'framework/collections/collection';
import TreeCollection from 'framework/collections/tree-collection';
import {GeoHash, calculateDistance, toLower, rawMongoQuery} from 'framework/helpers';
import Random from 'framework/random';

const treeCollections = [];

export default function (app) {
    app.set('collectionDefinitions', {});

    app.collection = (name, definition = null) => {
        if (name.indexOf('.') !== -1) {
            name = name.split('.').join('/');
        }

        if (!_.isObject(definition)) {
            if (_.includes(treeCollections, name)) {
                return new TreeCollection(app, app.service(name));
            } else {
                return new Collection(app, app.service(name));
            }
        } else {
            const options = {};

            // Disable paging by default.
            // @TODO -> Implement consistent pagination for security.
            // Client may request millions of records!
            options.paginate = _.isUndefined(definition.paginate) ? false : definition.paginate;

            // Extend schema for timestamps, uid, revisions, and soft delete.
            if (!definition.noDB) {
                if (_.isObject(definition.schema) && definition.timestamps !== false) {
                    definition.schema = _.assign(definition.schema, {
                        createdAt: {
                            type: 'datetime',
                            label: 'Created at',
                            required: false,
                            index: true
                        },
                        updatedAt: {
                            type: 'datetime',
                            label: 'Updated at',
                            required: false,
                            index: true
                        }
                    });
                }

                if (_.isObject(definition.schema) && definition.order === true) {
                    definition.schema = _.assign(definition.schema, {
                        order: {
                            type: 'decimal',
                            required: false,
                            index: true
                        }
                    });
                }

                if (_.isObject(definition.schema) && !_.isUndefined(definition.branch)) {
                    if (definition.branch === true || definition.branch === 'single') {
                        definition.schema = _.assign(definition.schema, {
                            branchId: {
                                type: 'string',
                                label: 'Branch office',
                                required: false,
                                index: true
                            }
                        });
                    } else if (definition.branch === 'multiple') {
                        definition.schema = _.assign(definition.schema, {
                            branchIds: {
                                type: ['string'],
                                label: 'Branch Offices',
                                required: false,
                                index: true
                            }
                        });
                    }
                }

                if (_.isObject(definition.schema) && definition.uid !== false) {
                    definition.schema = _.assign(definition.schema, {
                        createdBy: {
                            type: 'string',
                            label: 'Created by',
                            required: false,
                            index: true
                        },
                        updatedBy: {
                            type: 'string',
                            label: 'Updated by',
                            required: false,
                            index: true
                        }
                    });
                }

                if (_.isObject(definition.schema) && definition.assignable === true) {
                    definition.schema = _.assign(definition.schema, {
                        assignedBy: {
                            type: 'string',
                            required: false,
                            index: true
                        },
                        assignedTo: {
                            type: 'string',
                            required: false,
                            index: true
                        }
                    });
                }

                if (_.isObject(definition.schema) && definition.revisions === true) {
                    definition.schema = _.assign(definition.schema, {
                        revisionId: {
                            type: 'string',
                            required: false,
                            index: true
                        },
                        revisionName: {
                            type: 'string',
                            required: false
                        },
                        hasRevisions: {
                            type: 'boolean',
                            default: false
                        }
                    });
                }

                if (_.isObject(definition.schema) && _.isFunction(definition.copy)) {
                    definition.schema = _.assign(definition.schema, {
                        isCopy: {
                            type: 'boolean',
                            required: false
                        },
                        isRecurrence: {
                            type: 'boolean',
                            required: false
                        }
                    });
                }

                if (_.isObject(definition.schema) && definition.softDelete !== false) {
                    definition.schema = _.assign(definition.schema, {
                        deleted: {
                            type: 'boolean',
                            required: false,
                            index: true
                        },
                        deletedAt: {
                            type: 'datetime',
                            required: false,
                            index: true
                        },
                        deletedBy: {
                            type: 'string',
                            label: 'Deleted by',
                            required: false,
                            index: true
                        }
                    });
                }
            }

            // If this is a tree collection add it to tree collection list.
            if (definition.tree) {
                treeCollections.push(name);

                // Extend schema.
                if (_.isObject(definition.schema)) {
                    definition.schema = _.merge(definition.schema, {
                        tree: {
                            current: {type: 'string', default: ''},
                            parent: {type: 'string', default: ''},
                            path: {type: 'string', default: ''},
                            depth: {type: 'integer', default: 0},
                            hasChild: {type: 'boolean', default: false}
                        }
                    });
                }
            }

            // Has search terms.
            if (_.isFunction(definition.searchTerms)) {
                definition.schema = _.assign(definition.schema, {
                    searchTerms: {
                        type: ['string'],
                        label: 'Search terms',
                        required: false,
                        index: true
                    },
                    searchText: {
                        type: 'string',
                        label: 'Search text',
                        required: false,
                        index: true
                    }
                });
            }

            options.multi = true;

            if (Array.isArray(definition.events) && definition.events.length > 0) {
                options.events = definition.events;
            }

            if (definition.noDB) {
                options.id = '_id';

                const service = memoryService(options);
                service.isCollection = true;
                service.noDB = true;
                service.disableRest = _.isUndefined(definition.rest) ? true : !!definition.rest;
                if (_.isObject(definition.schema)) service.schema = definition.schema;

                app.use(name, service);
            } else {
                const collectionName = name.indexOf('/') !== -1 ? name.split('/').join('_') : name;

                options.Model = app.db.collection(collectionName);
                options.defaultLocale = app.config('app.locale');

                const service = dbService(options);
                service.isCollection = true;
                service.title = definition.title;
                service.path = name;
                service.collectionName = name.indexOf('/') !== -1 ? name.split('/').join('.') : name;
                service.noDB = false;
                service.softDelete = definition.softDelete !== false;
                service.assignable = definition.assignable === true;
                service.cache = definition.cache === true;
                service.localCache = definition.localCache === true;
                service.disableRest = _.isUndefined(definition.rest) ? true : !!definition.rest;
                service.sync = definition.sync !== false;
                if (_.isObject(definition.schema)) service.schema = definition.schema;
                if (_.isObject(definition.attributes)) service.attributes = definition.attributes;

                service.cacheManager = app.memoryCache;
                service.noCache = !!definition.noCache;
                service.db = app.db;

                app.use(name, service);

                const collectionDefinitions = app.get('collectionDefinitions');
                collectionDefinitions[name.split('/').join('.')] = definition;
                app.set('collectionDefinitions', collectionDefinitions);
            }

            const service = app.service(name);

            // Check if it has report schema.
            const allReportSchemas = Object.values(app.get('programSchemas') || {});
            const reportSchemas = allReportSchemas.filter(schema => schema.collection === name.split('/').join('.'));

            // Collation
            if (!definition.noDB && _.isObject(definition.schema)) {
                service.hooks({
                    before: {
                        all(context) {
                            context.params.collation = {
                                locale: app.config('app.locale')
                            };

                            return context;
                        }
                    }
                });
            }

            // Indexes
            // if (!definition.noDB && _.isObject(definition.schema) && app.isMaster && (app.get('isInitializing') || app.get('isPackaged'))) {
            // initIndexes(app, definition.Model, name, definition);
            // }

            // Unique data.
            if (_.isObject(definition.schema) && !_.isEmpty(definition.schema)) {
                service.hooks({
                    before: {
                        create: unique(definition.schema),
                        update: unique(definition.schema),
                        patch: unique(definition.schema)
                    }
                });
            }

            // Rich content data.
            if (_.isObject(definition.schema) && !_.isEmpty(definition.schema) && !!definition.hasRichContent) {
                service.hooks({
                    before: {
                        create: processRichContent(definition.schema),
                        update: processRichContent(definition.schema),
                        patch: processRichContent(definition.schema)
                    }
                });
            }

            // Translate fields.
            if (_.isObject(definition.schema) && !_.isEmpty(definition.schema)) {
                service.hooks({
                    after: {
                        find: translate(definition.schema),
                        get: translate(definition.schema)
                    }
                });
            }

            // Order data.
            if (_.isObject(definition.schema) && definition.order === true) {
                service.hooks({
                    before: {
                        find: applyOrdering,
                        create: applyOrdering
                    }
                });
            }

            // Search
            service.hooks({
                before(context) {
                    const query = context.params.query;

                    if (!!query && !!query['$search']) {
                        query.$text = {
                            $search: toLower(query['$search'])
                        };

                        delete query['$search'];
                    }

                    return context;
                }
            });

            // Branch.
            if (
                !definition.noDB &&
                _.isObject(definition.schema) &&
                !_.isUndefined(definition.branch) &&
                definition.branch !== false
            ) {
                service.hooks({
                    before: {
                        find: [checkBranch(definition)]
                    }
                });
            }

            // Timestamps
            if (!definition.noDB && definition.timestamps !== false) {
                service.hooks({
                    before: {
                        create: setNow('createdAt', 'updatedAt'),
                        update: setNow('updatedAt'),
                        patch: setNow('updatedAt')
                    }
                });
            }

            // UID
            if (!definition.noDB && definition.uid !== false) {
                service.hooks({
                    before: {
                        create: setUID
                    }
                });
            }

            // Revision
            if (!definition.noDB && definition.revisions === true) {
                const collection = name.split('/').join('.');

                service.hooks({
                    after: {
                        create: saveRevision(collection, 'create'),
                        update: saveRevision(collection, 'sync'),
                        patch: saveRevision(collection, 'sync')
                    }
                });
            }

            // Sync Numbering - *** Should be before soft delete ***
            if (!definition.noDB && !!definition.schema && !!definition.schema.numberingId) {
                service.hooks({
                    before: syncNumbering
                });
            }

            // Prevent deletion or deactivation of system records.
            if (
                _.isObject(definition.schema) &&
                !_.isEmpty(definition.schema) &&
                !_.isUndefined(definition.schema['system'])
            ) {
                service.hooks({
                    before: {
                        async patch(context) {
                            if (!!context.data) {
                                let data = context.data;

                                if (!!data.$set) {
                                    data = data.$set;
                                }

                                if (typeof data.isActive == 'boolean' && !data.isActive) {
                                    let query = null;
                                    if (!!context.id) {
                                        query = {_id: context.id};
                                    } else {
                                        query = (context.params || {}).query;
                                    }

                                    if (!!query) {
                                        const record = await app.collection(name.split('/').join('.')).findOne({
                                            ...query,
                                            $select: ['system'],
                                            $disableSoftDelete: true,
                                            $disableActiveCheck: true
                                        });

                                        if (!!record && !!record.system) {
                                            throw new app.errors.Unprocessable(
                                                app.translate('System records can not be deactivated!')
                                            );
                                        }
                                    }
                                }
                            }

                            return context;
                        },
                        async update(context) {
                            if (!!context.data && !!context.data.$set) {
                                const data = context.data.$set;

                                if (typeof data.isActive == 'boolean' && !data.isActive) {
                                    const record = await app.collection(name.split('/').join('.')).findOne({
                                        _id: context.id,
                                        $select: ['system'],
                                        $disableSoftDelete: true,
                                        $disableActiveCheck: true
                                    });

                                    if (!!record && !!record.system) {
                                        throw new app.errors.Unprocessable(
                                            app.translate('System records can not be deactivated!')
                                        );
                                    }
                                }
                            }

                            return context;
                        },
                        async remove(context) {
                            let query = null;
                            if (!!context.id) {
                                query = {_id: context.id};
                            } else {
                                query = (context.params || {}).query;
                            }

                            if (!!query) {
                                const record = await app.collection(name.split('/').join('.')).findOne({
                                    ...query,
                                    $select: ['system'],
                                    $disableSoftDelete: true,
                                    $disableActiveCheck: true
                                });

                                if (!!record && !!record.system) {
                                    throw new app.errors.Forbidden(
                                        app.translate('You cannot delete the records that are created by system!')
                                    );
                                }
                            }

                            return context;
                        }
                    }
                });
            }

            // Remove passive records.
            if (
                _.isObject(definition.schema) &&
                !_.isEmpty(definition.schema) &&
                !_.isUndefined(definition.schema['isActive'])
            ) {
                service.hooks({
                    before: {
                        find(context) {
                            if (context.params.disableActiveCheck) {
                                delete context.params.disableActiveCheck;

                                return context;
                            }

                            if (!!context.params.query && context.params.query.$disableActiveCheck) {
                                delete context.params.query.$disableActiveCheck;

                                return context;
                            }

                            if (!context.params.query) {
                                context.params.query = {};
                            }

                            context.params.query.isActive = true;

                            return context;
                        }
                    }
                });
            }

            // Set branch(s)
            if (!_.isUndefined(definition.branch)) {
                service.hooks({
                    before: {
                        create: [setBranch(definition.branch)]
                    }
                });
            }

            // Skip events functionality.
            service.hooks({
                before(context) {
                    if (!!context.params && !!context.params.skipEvents) {
                        context.event = null;

                        delete context.params.skipEvents;
                    }

                    return context;
                }
            });

            // Populate attributes.
            if (
                !definition.noDB &&
                _.isPlainObject(definition.attributes) &&
                Object.keys(definition.attributes).length > 0 &&
                (_.isFunction(definition.searchTerms) || reportSchemas.length > 0)
            ) {
                service.hooks({
                    after: {
                        create: populateAttributes(name.split('/').join('.'), definition.attributes),
                        update: populateAttributes(name.split('/').join('.'), definition.attributes),
                        patch: populateAttributes(name.split('/').join('.'), definition.attributes)
                    }
                });
            }

            // Has search text.
            if (_.isFunction(definition.searchTerms)) {
                service.hooks({
                    after: {
                        create: computeSearchTerms(definition, name.split('/').join('.')),
                        update: computeSearchTerms(definition, name.split('/').join('.')),
                        patch: computeSearchTerms(definition, name.split('/').join('.'))
                    }
                });
            }

            // Document location.
            if (!definition.noDB && !!definition.locationFrom) {
                service.hooks({
                    after: {
                        create: setDocumentLocation(name.split('/').join('.'), definition.locationFrom),
                        update: setDocumentLocation(name.split('/').join('.'), definition.locationFrom),
                        patch: setDocumentLocation(name.split('/').join('.'), definition.locationFrom)
                    }
                });
            }

            // Log ops.
            if (!definition.noDB && definition.title) {
                service.hooks({
                    after: {
                        create: logCollectionOps(name.split('/').join('.'), definition.title),
                        update: logCollectionOps(name.split('/').join('.'), definition.title),
                        patch: logCollectionOps(name.split('/').join('.'), definition.title),
                        remove: logCollectionOps(name.split('/').join('.'), definition.title)
                    }
                });
            }

            // Transfer tickets.
            if (!!app.hasModule('help-desk') && !!definition.view) {
                service.hooks({
                    after: {
                        create: transferTickets(name.split('/').join('.'), definition.view)
                    }
                });
            }

            // Joins and computed fields.
            if (!definition.noDB && !!definition.attributes) {
                const joins = initJoins(app, name.split('/').join('.'));

                service.hooks({
                    before: {
                        find: [joins.selectParentField]
                    },
                    after: {
                        all: [when(context => !context.params.skipJoin, fastJoin(joins.schema, joins.query))]
                    }
                });
            }

            // Hooks.
            if (_.isObject(definition.hooks)) {
                service.hooks(definition.hooks);
            }

            // Sync report. (Must be after service hooks. Service remove hooks comes before.)
            if (!definition.noDB && reportSchemas.length > 0) {
                service.hooks({
                    after: {
                        create: syncReport(name.split('/').join('.'), reportSchemas),
                        update: syncReport(name.split('/').join('.'), reportSchemas),
                        patch: syncReport(name.split('/').join('.'), reportSchemas),
                        remove: syncReport(name.split('/').join('.'), reportSchemas)
                    }
                });
            }

            // Soft delete. (Must be after service hooks. Service remove hooks comes before.)
            if (definition.softDelete !== false) {
                service.hooks({
                    before: {
                        all: [softDelete]
                    }
                });
            }

            // Service level publish.
            if (_.isFunction(definition.publish)) {
                service.publish((data, context) => {
                    if (!!app.get('disablePublications')) {
                        return false;
                    }

                    return definition.publish(app, data, context);
                });
            }
        }

        return app;
    };
}

function processRichContent(schema) {
    const contentFields = [];

    for (const key of Object.keys(schema)) {
        const s = schema[key];

        if (_.isPlainObject(s) && !!s.richContent) {
            contentFields.push(key);
        }
    }

    return async context => {
        if (contentFields.length < 1) return context;

        const app = context.app;
        const data = !!context.data.$set ? context.data.$set : context.data;

        if (!_.isPlainObject(data) || _.isEmpty(data)) {
            return context;
        }

        for (const contentField of contentFields) {
            const value = data[contentField];

            if (!_.isString(value) || _.isEmpty(value)) continue;

            const $ = cheerio.load(value);
            const images = $('img');

            // Get the file path.
            let directoryPath = app.get('isPackaged')
                ? app.setting('system.uploadDirectoryPath')
                : app.config('paths.files');
            if (directoryPath[0] !== '/') {
                directoryPath = path.join(process.cwd(), directoryPath);
            }

            for (const image of images) {
                const src = image.attribs.src;

                if (src.includes('data:image/')) {
                    let fileExtension = '';
                    if (src.includes('png;base64')) {
                        fileExtension = 'png';
                    } else if (src.includes('jpg;base64')) {
                        fileExtension = 'jpg';
                    } else if (src.includes('jpeg;base64')) {
                        fileExtension = 'jpeg';
                    } else if (src.includes('webp;base64')) {
                        fileExtension = 'webp';
                    } else if (src.includes('avif;base64')) {
                        fileExtension = 'avif';
                    } else if (src.includes('gif;base64')) {
                        fileExtension = 'gif';
                    }
                    if (!fileExtension) continue;
                    const filePath = path.join(directoryPath, `rci-${Random.id(16)}.${fileExtension}`);
                    const buffer = Buffer.from(src.split(';base64,').pop(), 'base64');
                    const file = await app.files.write(filePath, buffer);

                    image.attribs.src = app.absoluteUrl(`api/store/common/image/${file._id}.${fileExtension}`);
                }
            }

            data[contentField] = $('body').html();
        }

        return context;
    };
}

function populateAttributes(collectionName, attributes) {
    return async context => {
        const app = context.app;

        const $select = ['_id'];
        for (const field of Object.keys(attributes)) {
            const attribute = attributes[field];

            $select.push(attribute.parentField);
        }

        if ((Array.isArray(context.result) ? context.result : [context.result]).length > 0) {
            if (Array.isArray(context.result)) {
                const ids = context.result.map(item => item._id);
                const records = await app.collection(collectionName).find({
                    _id: {$in: ids},
                    $select,
                    $populate: Object.keys(attributes),
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
                const recordsMap = {};
                for (const record of records) {
                    recordsMap[record._id] = record;
                }

                context.result = context.result.map(item => {
                    const record = recordsMap[item._id];

                    if (!record) {
                        return item;
                    }

                    for (const field of Object.keys(attributes)) {
                        item[field] = record[field];
                    }

                    return item;
                });
            } else if (!!context.result && !!context.result._id) {
                const record = await app.collection(collectionName).findOne({
                    _id: context.result._id,
                    $select,
                    $populate: Object.keys(attributes),
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (!!record) {
                    for (const field of Object.keys(attributes)) {
                        context.result[field] = record[field];
                    }
                }
            }
        }

        return context;
    };
}

function checkBranch(definition) {
    return context => {
        const app = context.app;
        const user = context.params.user;
        const checkBranch = !!context.params.checkBranch;
        // const error = new app.errors.Forbidden(context.translate('You do not have sufficient permissions to perform this operation!'));

        if (!app.setting('system.multiBranch')) {
            return context;
        }

        // Don't check branch for the server request.
        if (!checkBranch && !context.params.provider) {
            return context;
        }

        if (!context.params.query) {
            context.params.query = {};
        }

        // If check disabled, skip.
        if (!!context.params.disableBranchCheck) {
            delete context.params.disableBranchCheck;

            return context;
        }
        if (!!context.params.query.$disableBranchCheck) {
            delete context.params.query.$disableBranchCheck;

            return context;
        }

        if (context.params.query.branchId) {
            return context;
        }

        // Check if the query has _id.
        const hasId = !!context.params.query._id && context.params.query._id.length > 0;
        if (hasId) return context;

        // Disallow un-authenticated request.
        if (!user || Object.keys(user).length < 1) {
            throw new app.errors.Forbidden(
                context.translate('You do not have sufficient permissions to perform this operation!')
            );
        }

        // Root user allowed all to do all operations.
        if (app.setting('system.rootsAuthorizedOnAllBranches') && user.isRoot) {
            return context;
        }

        if (!Array.isArray(context.params.query.$and)) {
            context.params.query.$and = [];
        }

        if (definition.branch === true || definition.branch === 'single') {
            context.params.query.$and.push({
                $or: [{branchId: {$exists: false}}, {branchId: {$eq: null}}, {branchId: {$in: user.branchIds}}]
            });
        } else if (definition.branch === 'multiple') {
            context.params.query.$and.push({
                $or: [{branchIds: {$exists: false}}, {branchIds: {$eq: null}}, {branchIds: {$in: user.branchIds}}]
            });
        }

        if (Array.isArray(context.params.query.$and) && context.params.query.$and.length < 1) {
            delete context.params.query.$and;
        }

        if (Array.isArray(context.params.query.$or) && context.params.query.$or.length < 1) {
            delete context.params.query.$or;
        }

        return context;
    };
}

async function syncNumbering(context) {
    if (context.path === 'kernel/numbering') {
        return context;
    }

    if (context.method === 'create') {
        let items = getItems(context);

        (Array.isArray(items) ? items : [items]).forEach(item => {
            if (_.isString(item.numberingId) && item.numberingId.length > 0) {
                context.app
                    .collection('kernel.numbering')
                    .patch({_id: item.numberingId}, {inUse: true}, {disableValidation: true});
            }
        });
    } else if (context.method === 'patch' || context.method === 'update') {
        const data = getItems(context).$set;

        if (_.isObject(data) && _.isString(data.numberingId) && data.numberingId.length > 0) {
            const service = context.service;
            const item = await service.get(context.id);

            if (_.isString(item.numberingId) && item.numberingId.length > 0) {
                await context.app
                    .collection('kernel.numbering')
                    .patch({_id: item.numberingId}, {inUse: false}, {disableValidation: true});
            }

            context.app
                .collection('kernel.numbering')
                .patch({_id: data.numberingId}, {inUse: true}, {disableValidation: true});
        }
    } else if (context.method === 'remove') {
        const service = context.service;
        let result = [];

        if (_.isString(context.id)) {
            result = [await service.get(context.id)];
        } else {
            result = await service.find(fastCopy(context.params));
        }

        for (const item of result) {
            if (_.isString(item.numberingId) && item.numberingId.length > 0) {
                await context.app
                    .collection('kernel.numbering')
                    .patch({_id: item.numberingId}, {inUse: false}, {disableValidation: true});
            }
        }
    }

    return context;
}

function setNow(...fieldNames) {
    if (!fieldNames.length) {
        throw new errors.BadRequest('Field name is required. (setNow)');
    }

    return context => {
        let items = getItems(context);

        if (context.method === 'patch' || context.method === 'update') {
            if (!_.isObject(items.$set)) items.$set = {};

            fieldNames.forEach(fieldName => {
                _.set(items.$set, fieldName, context.app.datetime.local().toJSDate());
            });
        } else {
            (Array.isArray(items) ? items : [items]).forEach(item => {
                fieldNames.forEach(fieldName => {
                    _.set(item, fieldName, context.app.datetime.local().toJSDate());
                });
            });
        }

        return context;
    };
}

async function initIndexes(app, collection, collectionName, definition) {
    const locale = app.config('app.locale');
    let indexes = findIndexes(definition.schema);

    if (Array.isArray(definition.extraIndexes) && definition.extraIndexes.length > 0) {
        const extraIndexes = [];

        for (const extraIndex of definition.extraIndexes) {
            if (extraIndex.type === 'normal' && extraIndex.key.includes('.*')) {
                extraIndex.key = extraIndex.key.replace('.*', '.$**');

                extraIndexes.push(extraIndex);
            } else {
                extraIndexes.push(extraIndex);
            }
        }

        // indexes = indexes.concat(definition.extraIndexes);
        indexes = indexes.concat(extraIndexes);
    }

    collectionName = collectionName.split('/').join('_');

    // Delete existing indexes. Fix: NamespaceNotFound with try catch.
    try {
        await app.db.collection(collectionName).dropIndexes();
    } catch (e) {}

    // Normal indexes.
    for (const i of indexes.filter(i => i.type === 'normal')) {
        let index = {};

        // const keyName = i.key.replace(/\./g, '_');
        const keyName = i.key;

        index[keyName] = i.value;

        await app.db.command({
            createIndexes: collectionName,
            indexes: [
                {
                    key: index,
                    name: `${keyName}_${i.value}`,
                    collation: {locale},
                    background: true
                }
            ]
        });
    }

    // Text indexes.
    const textIndexes = {};
    indexes
        .filter(i => i.type === 'text')
        .forEach(i => {
            if (!_.isString(textIndexes[i.key])) {
                textIndexes[i.key] = 'text';
            }
        });
    if (!_.isEmpty(textIndexes)) {
        const languages = {
            tr: 'turkish',
            en: 'english',
            de: 'german',
            fr: 'french',
            es: 'spanish',
            hu: 'hungarian',
            it: 'italian',
            ru: 'russian',
            ara: 'arabic'
        };

        const indexName =
            Object.keys(textIndexes)
                .map(key => key[0])
                .join('') + '_text';

        await app.db.command({
            createIndexes: collectionName,
            indexes: [
                {
                    key: textIndexes,
                    default_language: languages[locale] ? languages[locale] : 'english',
                    name: indexName,
                    background: true
                }
            ]
        });
    }

    // Geo indexes.
    const geoIndexes = {};
    indexes
        .filter(i => i.type === '2dsphere')
        .forEach(i => {
            if (!_.isString(geoIndexes[i.key])) {
                geoIndexes[i.key] = '2dsphere';
            }
        });
    if (!_.isEmpty(geoIndexes)) {
        const indexName =
            Object.keys(geoIndexes)
                .map(key => key[0])
                .join('') + '_2dsphere';

        await app.db.command({
            createIndexes: collectionName,
            indexes: [
                {
                    key: geoIndexes,
                    name: indexName,
                    background: true
                }
            ]
        });
    }
}

function findIndexes(schema, parentKey = '', indexes = []) {
    _.each(schema, (prop, key) => {
        if (Array.isArray(prop) && prop.every(p => _.isPlainObject(p))) {
            prop.forEach(p => {
                findIndexes(p, parentKey ? `${parentKey}.${key}` : key, indexes);
            });
        } else if (_.isPlainObject(prop) && Array.isArray(prop.type) && prop.type.every(p => _.isPlainObject(p))) {
            prop.type.forEach(p => {
                findIndexes(p, parentKey ? `${parentKey}.${key}` : key, indexes);
            });
        } else if (_.isPlainObject(prop) && _.isUndefined(prop.type)) {
            findIndexes(prop, parentKey ? `${parentKey}.${key}` : key, indexes);
        } else {
            if (_.isPlainObject(prop) && (!_.isUndefined(prop.index) || !_.isUndefined(prop.text))) {
                let definition = {};

                definition.type = !_.isUndefined(prop.text) ? 'text' : 'normal';
                definition.key = (parentKey ? `${parentKey}.${key}` : key).replace(/\.\$\./g, '.');
                definition.value = prop.index === true ? 1 : -1;

                indexes.push(definition);
            }
        }
    });

    return indexes;
}

function unique(schema) {
    const uniqueFields = findUniqueFields(schema);

    return async context => {
        if (uniqueFields.length > 0) {
            const service = context.service;
            const items = getItems(context);

            for (let data of Array.isArray(items) ? items : [items]) {
                if (context.method === 'patch' || context.method === 'update') {
                    data = data.$set;
                }

                for (let field of uniqueFields) {
                    const label = _.get(schema, field).label;
                    const value = _.get(data, field);

                    if (!_.isUndefined(value) && value !== '' && value !== null) {
                        if (context.id) {
                            let result = await service.find({
                                query: {
                                    [field]: value,
                                    $limit: 1,
                                    $select: ['_id']
                                },
                                disableActiveCheck: true
                            });

                            if (result.length > 0) {
                                result = result[0];

                                if (result._id !== context.id) {
                                    throw new context.app.errors.Unprocessable({
                                        field,
                                        message: context.translate('{{label}} must be unique!', {
                                            label: context.translate(label)
                                        })
                                    });
                                }
                            }
                        } else {
                            const result = await service.find({
                                query: {
                                    [field]: value,
                                    $limit: 0
                                },
                                paginate: {default: 100},
                                disableActiveCheck: true
                            });

                            if (result.total > 0) {
                                throw new context.app.errors.Unprocessable({
                                    field,
                                    message: context.translate('{{label}} must be unique!', {
                                        label: context.translate(label)
                                    })
                                });
                            }
                        }
                    }
                }
            }
        }

        return context;
    };
}

function findUniqueFields(schema, parentKey = '', fields = []) {
    _.each(schema, (prop, key) => {
        if (Array.isArray(prop) && prop.every(p => _.isPlainObject(p))) {
            prop.forEach(p => {
                findUniqueFields(p, parentKey ? `${parentKey}.${key}` : key + '[0]', fields);
            });
        } else if (_.isPlainObject(prop) && _.isUndefined(prop.type)) {
            findUniqueFields(prop, parentKey ? `${parentKey}.${key}` : key, fields);
        } else {
            if (_.isPlainObject(prop) && prop.unique) {
                fields.push(parentKey ? `${parentKey}.${key}` : key);
            }
        }
    });

    return fields;
}

function translate(schema) {
    const fieldsToTranslate = findFieldsToTranslate(schema);

    return context => {
        if (context.params.disableTranslation) {
            delete context.params.disableTranslation;

            return context;
        }

        if (fieldsToTranslate.length > 0) {
            let data = getItems(context);

            if (Array.isArray(data)) {
                for (let i = 0; i < data.length; i++) {
                    for (let field of fieldsToTranslate) {
                        const value = _.get(data[i], field);

                        if (!_.isUndefined(value)) {
                            data[i] = _.set(data[i], field, context.translate(value));
                        }
                    }
                }
            } else {
                for (let field of fieldsToTranslate) {
                    const value = _.get(data, field);

                    if (!_.isUndefined(value)) {
                        _.set(data, field, context.translate(value));
                    }
                }
            }

            replaceItems(context, data);
        }

        return context;
    };
}

function findFieldsToTranslate(schema, parentKey = '', fields = []) {
    _.each(schema, (prop, key) => {
        if (Array.isArray(prop) && prop.every(p => _.isPlainObject(p))) {
            prop.forEach(p => {
                findFieldsToTranslate(p, parentKey ? `${parentKey}.${key}` : key, fields);
            });
        } else if (_.isPlainObject(prop) && _.isUndefined(prop.type)) {
            return findFieldsToTranslate(prop, parentKey ? `${parentKey}.${key}` : key, fields);
        } else {
            if (_.isPlainObject(prop) && prop.translate) {
                fields.push(parentKey ? `${parentKey}.${key}` : key);
            }
        }
    });

    return fields;
}

async function applyOrdering(context) {
    if (context.params.disableOrdering) {
        delete context.params.disableOrdering;

        return context;
    }
    if (!!context.params.query && context.params.query.$disableOrdering) {
        delete context.params.query.$disableOrdering;

        return context;
    }

    if (context.method === 'create') {
        let max = await context.app.rpc('kernel.database.max', {
            servicePath: context.path,
            field: 'order',
            query: {}
        });
        let items = getItems(context);

        if (_.isNull(max)) max = 0;

        (Array.isArray(items) ? items : [items]).forEach(item => {
            if (!_.isNumber(item.order)) {
                _.set(item, 'order', ++max);
            }
        });
    } else if (context.method === 'find') {
        let sorts = [];
        let sort = {};

        if (_.isObject(context.params.query)) {
            if (Array.isArray(context.params.query.$sort)) {
                sorts = sorts.concat(context.params.query.$sort);
            } else if (_.isObject(context.params.query.$sort)) {
                sorts.push(context.params.query.$sort);
            }
        } else {
            context.params.query = {};
        }

        if (Array.isArray(context.params.query.$select)) {
            context.params.query.$select = _.uniq(context.params.query.$select.concat(['order']));
        }

        sorts.unshift({order: 1});

        _.each(sorts, s => {
            _.each(s, (value, key) => {
                sort[key] = value;
            });
        });

        context.params.query.$sort = sort;
    }

    return context;
}

function setUID(context) {
    if (
        _.isObject(context.params) &&
        _.isObject(context.params.user) &&
        !_.isEmpty(context.params.user) &&
        _.isString(context.params.user._id)
    ) {
        let items = getItems(context);

        if (context.method === 'patch' || context.method === 'update') {
            if (!_.isObject(items.$set)) items.$set = {};

            _.set(items.$set, 'updatedBy', context.params.user._id);
        } else if (context.method === 'create') {
            (Array.isArray(items) ? items : [items]).forEach(item => {
                _.set(item, 'createdBy', context.params.user._id);
            });
        }
    }

    return context;
}

function saveRevision(collection, operation) {
    return context => {
        const app = context.app;
        const service = context.service;
        const document = context.result;
        const revisionsCollection = app.collection('kernel.revisions');
        let user = null;

        if (
            _.isObject(context.params) &&
            _.isObject(context.params.user) &&
            !_.isEmpty(context.params.user) &&
            _.isString(context.params.user._id)
        ) {
            user = context.params.user;
        }

        if (_.isNull(user) || context.params.disableRevisions) {
            return context;
        }

        (async () => {
            if (operation === 'create') {
                const revisionCount = await revisionsCollection.count({
                    documentId: document._id,
                    collection
                });

                if (revisionCount < 1) {
                    // First revision.
                    const revision = await revisionsCollection.create({
                        documentId: document._id,
                        collection,
                        order: 0,
                        name: 'Rev 0',
                        reason: app.translate('Initial revision'),
                        userId: user._id,
                        date: app.datetime.local().toJSDate(),
                        document
                    });

                    service.patch(
                        document._id,
                        {
                            revisionId: revision._id,
                            revisionName: revision.name
                        },
                        {user, disableRevisions: true}
                    );
                }
            } else if (operation === 'sync') {
                revisionsCollection.patch(
                    {_id: document.revisionId},
                    {
                        userId: user._id,
                        date: app.datetime.local().toJSDate(),
                        document
                    }
                );
            }
        })();

        return context;
    };
}

function softDelete(context) {
    const service = context.service;

    context.data = context.data || {};
    context.params.query = context.params.query || {};

    if (context.params.query.$disableSoftDelete) {
        delete context.params.query.$disableSoftDelete;

        return context;
    }

    if (context.params.disableSoftDelete) {
        delete context.params.disableSoftDelete;

        return context;
    }

    switch (context.method) {
        case 'find':
            context.params.query.deleted = {$ne: true};

            return context;
        case 'get':
            return throwIfItemDeleted(context.id, true).then(data => {
                context.result = data;

                return context;
            });
        case 'create': // fall through
        case 'update':
        case 'patch':
            // if (context.id !== null) {
            //     return throwIfItemDeleted(context.id).then(() => context);
            // }
            //
            // context.params.query.deleted = {$ne: true};

            return context;
        case 'remove':
            return Promise.resolve()
                .then(() => (context.id ? throwIfItemDeleted(context.id) : null))
                .then(() => {
                    context.data.deleted = true;
                    context.data.deletedAt = context.app.datetime.local().toJSDate();
                    context.params.query.deleted = {$ne: true};
                    context.params.query.$disableSoftDelete = true;

                    if (
                        _.isObject(context.params) &&
                        _.isObject(context.params.user) &&
                        !_.isEmpty(context.params.user) &&
                        _.isString(context.params.user._id)
                    ) {
                        context.data.deletedBy = context.params.user._id;
                    }

                    return service.patch(context.id, context.data, context.params).then(result => {
                        context.result = result;

                        if (!context.app.get('disablePublications')) {
                            service.emit('removed', result);
                        }

                        return context;
                    });
                });
    }

    function throwIfItemDeleted(id, isGet) {
        const params = isGet
            ? context.params
            : {
                  query: {},
                  provider: context.params.provider,
                  _populate: 'skip',
                  authentication: context.params.authentication,
                  user: context.params.user
              };

        params.query.$disableSoftDelete = true;

        return service.get(id, params).then(data => {
            delete params.query.$disableSoftDelete;

            if (data.deleted) {
                throw new context.app.errors.NotFound('Item not found.');
            }

            return data;
        });
    }
}

function setBranch(branch) {
    return async function (context) {
        const items = getItems(context);

        for (const item of Array.isArray(items) ? items : [items]) {
            if (_.isUndefined(item.branchId) && (branch === true || branch === 'single')) {
                item.branchId = await findBranch(context, true);
            } else if (_.isUndefined(item.branchIds) && branch === 'multiple') {
                item.branchIds = await findBranch(context);
            }
        }

        return context;
    };
}

async function findBranch(context, getMainBranch = false) {
    if (getMainBranch) {
        if (
            _.isObject(context.params) &&
            _.isObject(context.params.user) &&
            _.isString(context.params.user.mainBranchId)
        ) {
            return context.params.user.mainBranchId;
        }

        return (
            await context.app.collection('kernel.branches').findOne({
                system: true,
                $select: ['_id']
            })
        )._id;
    } else {
        if (
            _.isObject(context.params) &&
            _.isObject(context.params.user) &&
            Array.isArray(context.params.user.branchIds)
        ) {
            return context.params.user.branchIds;
        }

        const branch = await context.app.collection('kernel.branches').findOne({
            system: true,
            $select: ['_id']
        });

        return [branch._id];
    }
}

function computeSearchTerms(definition, collectionName) {
    return context => {
        // Should be run after some time for async collection hooks..
        setTimeout(async () => {
            const app = context.app;
            const result = Array.isArray(context.result) ? context.result : [context.result];

            for (const item of result) {
                const document = item;

                if (_.isPlainObject(document)) {
                    let values =
                        (await definition.searchTerms.call(
                            {
                                app,
                                translate: context.translate
                            },
                            document
                        )) || [];

                    values = values
                        .filter(v => !_.isEmpty(v))
                        .map(v => {
                            if (_.isDate(v)) {
                                v = app.format(v, 'datetime');
                            }

                            if (!_.isString(v) && !!v.toString) {
                                v = v.toString();
                            }

                            return v.trim();
                        });

                    if (values.length > 0) {
                        values = _.uniq(values).join(' ');

                        const terms = ngrams(values);

                        // Persist local search text.
                        await app.db.collection(collectionName.split('.').join('_')).updateOne(
                            {_id: new ObjectId(item._id)},
                            {
                                $set: {searchTerms: terms, searchText: values}
                            },
                            {
                                collation: {locale: app.config('app.locale')}
                            }
                        );

                        // Persist search index.
                        if (!!definition.title && !!definition.view) {
                            const searchIndex = {};

                            searchIndex.collection = collectionName;
                            searchIndex.view = definition.view;
                            searchIndex.terms = terms;
                            searchIndex.text = values;
                            if (Array.isArray(document.branchIds)) {
                                searchIndex.branchIds = document.branchIds;
                            } else if (!!document.branchId) {
                                searchIndex.branchIds = [document.branchId];
                            } else {
                                searchIndex.branchIds = null;
                            }

                            const publicParams = app.get('publicParams');
                            const program = publicParams.programs.find(p => p.name === collectionName.split('.')[0]);
                            let programTitle = '';
                            if (_.isObject(program) && program.title) {
                                programTitle = program.title;
                            } else {
                                programTitle = 'System';
                            }
                            searchIndex.collectionName = `${context.app.translate(programTitle)} / ${context.translate(
                                definition.title
                            )}`;

                            searchIndex.documentId = item._id;
                            searchIndex.documentCreatedAt = item.createdAt;

                            if (item.code && item.name) {
                                searchIndex.documentIdentifier = `${item.code} - ${item.name}`;
                            } else if (item.code) {
                                searchIndex.documentIdentifier = item.code;
                            } else if (item.name) {
                                searchIndex.documentIdentifier = item.name;
                            } else if (item.title) {
                                searchIndex.documentIdentifier = item.title;
                            } else if (item.label) {
                                searchIndex.documentIdentifier = item.label;
                            } else if (item.voucherNo) {
                                searchIndex.documentIdentifier = item.voucherNo;
                            } else if (item.documentNo) {
                                searchIndex.documentIdentifier = item.documentNo;
                            }
                            if (!searchIndex.documentIdentifier) continue;

                            searchIndex.createdBy = item.createdBy;
                            searchIndex.responsibleId = item.assignedTo ? item.assignedTo : item.createdBy;
                            if (!searchIndex.responsibleId) {
                                searchIndex.responsibleId = null;
                            }

                            const existing = await app.collection('kernel.search-indexes').findOne({
                                documentId: item._id,
                                $select: ['_id']
                            });

                            if (_.isPlainObject(existing)) {
                                await app.db.collection('kernel_search-indexes').updateOne(
                                    {_id: new ObjectId(existing._id)},
                                    {
                                        $set: searchIndex
                                    },
                                    {
                                        collation: {locale: app.config('app.locale')}
                                    }
                                );
                            } else {
                                await app.db.collection('kernel_search-indexes').insertOne(searchIndex);
                            }
                        }
                    }
                }
            }
        }, 5000);

        return context;
    };
}

function setDocumentLocation(collectionName, locationFrom) {
    return context => {
        if (context.params.disableSetDocumentLocation) {
            delete context.params.disableSetDocumentLocation;

            return context;
        }

        if (!context.params.setLocation) {
            return context;
        }

        (async () => {
            const app = context.app;
            const user = context.params.user;
            const userLocation = context.params.userLocation;
            const googleMapsApiKey = app.setting('system.googleMapsApiKey');

            if (_.isString(googleMapsApiKey) && !!googleMapsApiKey.trim()) {
                for (const item of Array.isArray(context.result) ? context.result : [context.result]) {
                    const address = _.get(item, locationFrom);

                    if (_.isObject(address) && _.isString(address.address) && address.address.length > 0) {
                        try {
                            const geocoded = await app.geocode(address.address);
                            const latitude = geocoded.lat;
                            const longitude = geocoded.lng;

                            if (_.isNumber(latitude) && _.isNumber(longitude)) {
                                const dl = {};

                                dl.location = {
                                    type: 'Point',
                                    coordinates: [longitude, latitude]
                                };
                                dl.locationLat = latitude;
                                dl.locationLng = longitude;
                                dl.locationHash = GeoHash.encode(latitude, longitude);

                                if (!!userLocation) {
                                    if (context.method === 'create') {
                                        dl.createLocation = {
                                            type: 'Point',
                                            coordinates: [userLocation.longitude, userLocation.latitude]
                                        };
                                        dl.createLocationLat = userLocation.latitude;
                                        dl.createLocationLng = userLocation.longitude;
                                        dl.locationToCreateDistance = calculateDistance(
                                            latitude,
                                            longitude,
                                            userLocation.latitude,
                                            userLocation.longitude
                                        );
                                    } else if (context.method === 'update' || context.method === 'patch') {
                                        dl.updateLocation = {
                                            type: 'Point',
                                            coordinates: [userLocation.longitude, userLocation.latitude]
                                        };
                                        dl.updateLocationLat = userLocation.latitude;
                                        dl.updateLocationLng = userLocation.longitude;
                                        dl.locationToUpdateDistance = calculateDistance(
                                            latitude,
                                            longitude,
                                            userLocation.latitude,
                                            userLocation.longitude
                                        );
                                    }
                                }

                                dl.documentCollection = collectionName;
                                dl.documentId = item._id;
                                dl.document = {};
                                if (!!item.status) dl.document.status = item.status;
                                if (!!item.code) dl.document.code = item.code;
                                if (!!item.type) dl.document.type = item.type;
                                if (!!item.documentTypeId) dl.document.documentTypeId = item.documentTypeId;
                                if (_.isDate(item.issueDate)) dl.document.date = item.issueDate;
                                else if (_.isDate(item.quotationDate)) dl.document.date = item.quotationDate;
                                else if (_.isDate(item.orderDate)) dl.document.date = item.orderDate;
                                else if (_.isDate(item.startDate)) dl.document.date = item.startDate;
                                else if (_.isDate(item.recordDate)) dl.document.date = item.recordDate;
                                else if (_.isDate(item.date)) dl.document.date = item.date;
                                else if (_.isDate(item.createdAt)) dl.document.date = item.createdAt;
                                if (!!item.sourceId) dl.document.sourceId = item.sourceId;
                                if (!!item.communicationChannelId)
                                    dl.document.communicationChannelId = item.communicationChannelId;
                                if (!!item.branchId) dl.document.branchId = item.branchId;
                                if (!!item.organizationId) dl.document.organizationId = item.organizationId;
                                if (!!item.salesManagerId) dl.document.salesManagerId = item.salesManagerId;
                                if (!!item.salespersonId) dl.document.salespersonId = item.salespersonId;
                                if (_.isNumber(item.grandTotal)) dl.document.total = item.grandTotal;
                                else if (_.isNumber(item.potentialAmount)) dl.document.total = item.potentialAmount;
                                else if (_.isNumber(item.total)) dl.document.total = item.total;

                                if (_.isNumber(dl.document.total) && _.isNumber(item.currencyRate)) {
                                    dl.document.total *= item.currencyRate;
                                }

                                if (_.isPlainObject(user)) {
                                    dl.userId = user._id;
                                }

                                const existing = await app.collection('kernel.document-locations').findOne({
                                    documentCollection: dl.documentCollection,
                                    documentId: dl.documentId,
                                    $select: ['_id', 'createLocation', 'updateLocation']
                                });
                                const params = _.isPlainObject(user) ? {user} : {};
                                params.disableValidation = true;

                                if (_.isPlainObject(existing)) {
                                    const data = {...dl};

                                    if (!!existing.createLocation && !!existing.updateLocation) {
                                        data.createToUpdateDistance = calculateDistance(
                                            existing.createLocation.coordinates[1],
                                            existing.createLocation.coordinates[0],
                                            existing.updateLocation.coordinates[1],
                                            existing.updateLocation.coordinates[0]
                                        );
                                    }

                                    await app.collection('kernel.document-locations').patch({_id: existing._id}, data, {
                                        ...params,
                                        disableValidation: true
                                    });
                                } else {
                                    await app.collection('kernel.document-locations').create(dl, {
                                        ...params,
                                        disableValidation: true
                                    });
                                }
                            }
                        } catch (error) {
                            console.error(error);
                        }
                    }
                }
            }
        })();

        return context;
    };
}

function logCollectionOps(collectionName, collectionLabel) {
    return context => {
        (async () => {
            const user = context.params.user;

            if (_.isObject(user)) {
                const log = {};
                let message = '';
                let method = null;

                if (context.method === 'create') {
                    method = 'create';
                    message = context.app.translate('Record created successfully.');
                } else if (context.method === 'update' || context.method === 'patch') {
                    method = 'update';
                    message = context.app.translate('Record updated successfully.');
                } else if (context.method === 'remove') {
                    method = 'remove';
                    message = context.app.translate('Record deleted successfully.');
                }

                log.level = 'success';
                log.message = message;
                log.date = context.app.datetime.local().toJSDate();
                log.userId = user._id;
                log.user = {
                    name: user.name,
                    email: user.email
                };

                if (method) {
                    log.method = method;
                }

                if (collectionName) {
                    log.collection = collectionName;

                    if (_.isObject(context.result) && _.isString(context.result._id))
                        log.documentId = context.result._id;

                    const publicParams = context.app.get('publicParams');
                    const program = publicParams.programs.find(p => p.name === collectionName.split('.')[0]);
                    let programTitle = '';
                    if (_.isObject(program) && program.title) {
                        programTitle = program.title;
                    } else {
                        programTitle = 'System';
                    }

                    log.collectionName = `${context.app.translate(programTitle)} / ${context.app.translate(
                        collectionLabel
                    )}`;

                    if (_.isObject(context.result)) {
                        const data = context.result;
                        let documentIdentifier = '';

                        if (data.code && data.name) {
                            documentIdentifier = `${data.code} - ${data.name}`;
                        } else if (data.code) {
                            documentIdentifier = data.code;
                        } else if (data.name) {
                            documentIdentifier = data.name;
                        } else if (data.title) {
                            documentIdentifier = data.title;
                        } else if (data.label) {
                            documentIdentifier = data.label;
                        }

                        if (documentIdentifier) {
                            log.documentIdentifier = documentIdentifier;
                        }
                    }
                }

                context.app.collection('kernel.logs').create(log, {disableValidation: true});
            }
        })();

        return context;
    };
}

function syncReport(collectionName, schemas) {
    return async context => {
        const app = context.app;
        const db = app.reportDb;
        const user = context.params.user;

        if (!db) return context;

        try {
            for (const document of Array.isArray(context.result) ? context.result : [context.result]) {
                for (const schema of schemas) {
                    if (!schema.data || !schema.collection || !!schema.sql) {
                        continue;
                    }

                    if (_.isFunction(schema.query)) {
                        const query = await schema.query(app);

                        if (_.isPlainObject(query)) {
                            if ([document].filter(sift(rawMongoQuery(query))).length < 1) {
                                continue;
                            }
                        }
                    }

                    await app.rpc('kernel.common.sync-collection-op-to-report-db', {
                        schemaName: schema.name,
                        document,
                        operation: context.method,
                        user
                    });
                }
            }
        } catch (error) {
            console.log(error.message);
        }

        return context;
    };
}

function transferTickets(collectionName, collectionView) {
    return async context => {
        const app = context.app;
        const user = context.params.user;

        if (!user) return context;

        try {
            for (const document of Array.isArray(context.result) ? context.result : [context.result]) {
                if (!Array.isArray(document.relatedDocuments) || document.relatedDocuments.length < 1) {
                    continue;
                }

                const ticketCodes = [];

                for (const rd of document.relatedDocuments) {
                    const rdCollection = rd.collection;

                    for (const id of rd.ids || []) {
                        const transaction = await app.collection('help-desk.ticket-transactions').findOne({
                            collectionName: rdCollection,
                            documentId: id,
                            $select: ['_id', 'ticketCode']
                        });

                        if (!!transaction) {
                            ticketCodes.push(transaction.ticketCode);
                        }
                    }
                }

                for (const ticketCode of ticketCodes) {
                    await app.rpc(
                        'help-desk.create-ticket-transaction',
                        {
                            ticketCode,
                            documentCollection: collectionName,
                            documentId: document._id,
                            documentView: collectionView
                        },
                        {user}
                    );
                }
            }
        } catch (error) {
            console.log(error);
        }

        return context;
    };
}

function initJoins(app, collectionName) {
    const {getResultsByKey, getUniqueKeys} = BatchLoader;
    const collection = app.collection(collectionName);
    const loaders = [];
    const joins = {};

    _.each(collection.attributes, (properties, field) => {
        // Property checks.
        if (!_.isFunction(properties) && !_.isString(properties.collection))
            throw new Error('Please provide the collection that will be used to populate this collection!');
        if (!_.isFunction(properties) && !_.isString(properties.parentField))
            throw new Error('Please provide the population parent field!');
        if (!_.isFunction(properties) && !_.isString(properties.childField))
            throw new Error('Please provide the population child field!');

        if (!_.isFunction(properties)) {
            // Join
            const parentFieldSchema = _.get(collection.schema, properties.parentField);
            const loadFn =
                properties.parentField === '_id' ||
                Array.isArray(parentFieldSchema) ||
                (_.isObject(parentFieldSchema) && Array.isArray(parentFieldSchema.type))
                    ? 'loadMany'
                    : 'load';

            // Create loaders.
            loaders.push({
                field,
                service: properties.collection.split('.').join('/'),
                parentField: properties.parentField,
                childField: properties.childField,
                hasMany: properties.parentField === '_id',
                extraQuery: properties.query || {},
                auto: !!properties.auto
            });

            // Create joins.
            joins[field] = {};
            joins[field].resolver = () => async (record, context) => {
                const loader = context._loaders[field][properties.childField];

                if (properties.parentField === '_id') {
                    const params = [record[properties.parentField]];

                    if (_.isUndefined(params)) {
                        return;
                    }

                    record[field] = (await loader[loadFn](params))[0];
                } else {
                    const params =
                        properties.parentField.indexOf('.') !== -1
                            ? _.get(record, properties.parentField)
                            : record[properties.parentField];

                    if (_.isUndefined(params)) {
                        return;
                    }

                    record[field] = await loader[loadFn](params);
                }
            };
        } else {
            // Computed
            joins[field] = () => async record => (record[field] = await properties.call({app, collection}, record));
        }
    });

    return {
        schema: {
            before: context => {
                context._loaders = {};

                loaders.forEach(loader => {
                    context._loaders[loader.field] = {};

                    context._loaders[loader.field][loader.childField] = new BatchLoader(
                        async (keys, context) => {
                            const params = makeCallingParams(context, {
                                [loader.childField]: {
                                    $in: getUniqueKeys(keys)
                                }
                            });

                            delete params.provider;

                            // Disable circular joins.
                            params.skipJoin = true;

                            // Disable soft delete and active check.
                            params.query.$disableSoftDelete = true;
                            params.query.$disableActiveCheck = true;
                            params.disableActiveCheck = true;
                            params.disableInUseCheck = true;
                            params.disablePermissionCheck = true;
                            params.disableBranchCheck = true;

                            // Extra query.
                            let populateQuery = {};
                            let fieldsToSelect = [];
                            if (!loader.auto && _.isObject(context.params.query)) {
                                const populate = context.params.query.$populate;
                                if (Array.isArray(populate)) {
                                    const populateField = populate.find(p => p.field === loader.field);

                                    if (_.isObject(populateField)) {
                                        fieldsToSelect.push(populateField.field);

                                        if (_.isObject(populateField.query)) {
                                            populateQuery = populateField.query;
                                        }
                                    }
                                } else if (_.isObject(populate)) {
                                    fieldsToSelect.push(populate.field);

                                    if (_.isObject(populate.query)) {
                                        populateQuery = populate.query;
                                    }
                                }
                            }

                            if (_.isObject(params.query) && Array.isArray(params.query.$select)) {
                                params.query.$select = _.uniq(params.query.$select.concat(fieldsToSelect));
                            }

                            params.query = _.merge(params.query, loader.extraQuery, populateQuery);

                            const service = app.service(loader.service);
                            const result = await service.find(params);

                            return getResultsByKey(
                                keys,
                                result,
                                record => record[loader.childField],
                                loader.hasMany ? '[]' : '!'
                            );
                        },
                        {context}
                    );
                });
            },
            joins
        },
        query: context => {
            const query = {};

            _.each(collection.attributes, (properties, field) => {
                if (!_.isFunction(properties)) {
                    if (properties.auto) {
                        query[field] = true;
                    }
                } else {
                    query[field] = true;
                }
            });

            if (!!context.params && !!context.params.query) {
                const populate = context.params.query.$populate;

                if (!_.isUndefined(populate)) {
                    if (_.isString(populate)) {
                        query[populate] = true;
                    } else if (Array.isArray(populate)) {
                        populate.forEach(p => {
                            if (_.isString(p)) {
                                query[p] = true;
                            } else if (_.isObject(p) && !_.isUndefined(p.field)) {
                                query[p.field] = true;
                            }
                        });
                    } else if (_.isObject(populate) && !_.isUndefined(populate.field)) {
                        query[populate.field] = true;
                    }
                }
            }

            return query;
        },
        selectParentField(context) {
            const query = context.params.query;

            if (!!query && !!query.$populate && Array.isArray(query.$select)) {
                const populate = context.params.query.$populate;
                const populations = [];

                if (!!populate) {
                    if (_.isString(populate)) {
                        populations.push(populate);
                    } else if (Array.isArray(populate)) {
                        for (const p of populate) {
                            if (_.isString(p)) {
                                populations.push(p);
                            } else if (!!p && !!p.field) {
                                populations.push(p.field);
                            }
                        }
                    } else if (!!populate && !!populate.field) {
                        populations.push(populate.field);
                    }
                }

                for (const field of populations) {
                    const attribute = collection.attributes[field];

                    if (!!attribute && !!attribute.parentField && query.$select.indexOf(attribute.parentField) === -1) {
                        query.$select.push(attribute.parentField);
                    }
                }
            }

            if (!!collection.attributes && !!context.params.query) {
                const query = context.params.query;

                if (Array.isArray(query.$select)) {
                    for (const field of Object.keys(collection.attributes)) {
                        const attribute = collection.attributes[field];

                        if (!!attribute && !!attribute.auto && query.$select.indexOf(attribute.parentField) === -1) {
                            query.$select.push(attribute.parentField);
                        }
                    }
                }
            }

            return context;
        }
    };
}
